# TradingAgents Frontend Docker 部署指南

## 📋 前置要求

- Docker Desktop (Windows/Mac) 或 Docker Engine (Linux)
- Docker Compose v2.0+

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

**Windows (PowerShell):**
```powershell
cd docker
.\start.ps1
```

**Linux/Mac (Bash):**
```bash
cd docker
chmod +x start.sh
./start.sh
```

### 方法二：手动启动

1. **配置环境变量**
   ```bash
   cd docker
   cp .env.example .env
   # 编辑 .env 文件，填入你的 API 密钥
   ```

2. **选择启动模式**

   **仅前端服务（推荐用于开发）:**
   ```bash
   docker-compose -f docker-compose-simple.yml up --build -d
   ```

   **完整服务（前端 + MySQL + Nginx）:**
   ```bash
   docker-compose up --build -d
   ```

   **前端 + 数据库:**
   ```bash
   docker-compose up frontend mysql --build -d
   ```

## 📁 文件说明

- `Dockerfile` - 前端应用的 Docker 镜像构建文件
- `docker-compose.yml` - 完整服务编排文件
- `docker-compose-simple.yml` - 仅前端服务编排文件
- `docker-compose-frontend-only.yml` - 开发模式前端服务
- `.env.example` - 环境变量模板
- `start.ps1` - Windows PowerShell 启动脚本
- `start.sh` - Linux/Mac Bash 启动脚本
- `nginx.conf` - Nginx 反向代理配置

## 🔧 服务配置

### 前端服务 (Frontend)
- **端口**: 3000
- **访问地址**: http://localhost:3000
- **环境**: 生产模式 (NODE_ENV=production)

### MySQL 数据库 (可选)
- **端口**: 13306 (映射到容器内的 3306)
- **数据库**: trading_agents
- **用户**: trading_user
- **密码**: trading123 (可在 .env 中修改)

### Nginx 反向代理 (可选)
- **HTTP 端口**: 80
- **HTTPS 端口**: 443
- **配置文件**: `nginx.conf`

## 🛠️ 常用命令

### 查看服务状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f frontend
```

### 停止服务
```bash
docker-compose down
```

### 重新构建并启动
```bash
docker-compose up --build -d
```

### 清理所有容器和镜像
```bash
docker-compose down --rmi all --volumes --remove-orphans
```

## 🔍 故障排除

### 1. 端口冲突
如果 3000 端口被占用，修改 docker-compose.yml 中的端口映射：
```yaml
ports:
  - "3000:3000"  # 将本地端口改为 3000
```

### 2. 权限问题
在 Linux/Mac 上，确保启动脚本有执行权限：
```bash
chmod +x start.sh
```

### 3. 构建失败
清理 Docker 缓存并重新构建：
```bash
docker system prune -a
docker-compose build --no-cache
```

### 4. 环境变量问题
确保 `.env` 文件存在且包含必要的 API 密钥：
```bash
# 检查环境变量
docker-compose config
```

## 📝 环境变量说明

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `NEXT_PUBLIC_OPENAI_API_KEY` | OpenAI API 密钥 | - |
| `NEXT_PUBLIC_FINNHUB_API_KEY` | Finnhub API 密钥 | - |
| `NEXT_PUBLIC_API_BASE_URL` | 后端 API 地址 | http://localhost:5000 |
| `NEXT_PUBLIC_WS_URL` | WebSocket 地址 | ws://localhost:8000 |
| `MYSQL_ROOT_PASSWORD` | MySQL root 密码 | trading123 |
| `MYSQL_DATABASE` | 数据库名称 | trading_agents |

## 🔗 相关链接

- [Docker 官方文档](https://docs.docker.com/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [Next.js Docker 部署](https://nextjs.org/docs/deployment#docker-image)
