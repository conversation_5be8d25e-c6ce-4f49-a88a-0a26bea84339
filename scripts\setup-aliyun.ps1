# 阿里云容器镜像服务快速设置脚本 (PowerShell)
# 用于配置本地 Docker 环境和测试阿里云镜像推送

param(
    [switch]$TestOnly,
    [switch]$CleanupOnly,
    [switch]$SkipBuild,
    [switch]$SkipPush,
    [switch]$SkipTest,
    [switch]$Help
)

# 阿里云配置
$AliyunRegistry = "crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com"
$AliyunUsername = "aliyun1315382626"
$AliyunPassword = "ezreal123"
$AliyunNamespace = "ez_trading"
$AliyunRepo = "frontend"

# 颜色定义
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    White = "White"
}

# 日志函数
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "Info"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    switch ($Level) {
        "Info" { 
            Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor $Colors.Blue
        }
        "Success" { 
            Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor $Colors.Green
        }
        "Warning" { 
            Write-Host "[$timestamp] [WARNING] $Message" -ForegroundColor $Colors.Yellow
        }
        "Error" { 
            Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor $Colors.Red
        }
    }
}

# 显示欢迎信息
function Show-Welcome {
    @"
╔══════════════════════════════════════════════════════════════╗
║                阿里云容器镜像服务设置                         ║
║              TradingAgents Frontend                          ║
╚══════════════════════════════════════════════════════════════╝

此脚本将帮助您：
✓ 配置阿里云容器镜像服务登录
✓ 测试镜像推送和拉取
✓ 验证 CI/CD 配置

"@
}

# 显示配置信息
function Show-Config {
    Write-Log "阿里云容器镜像服务配置：" "Info"
    Write-Host "  注册表地址: $AliyunRegistry"
    Write-Host "  用户名: $AliyunUsername"
    Write-Host "  命名空间: $AliyunNamespace"
    Write-Host "  仓库名称: $AliyunRepo"
    Write-Host ""
}

# 显示帮助信息
function Show-Help {
    @"
用法: .\setup-aliyun.ps1 [选项]

选项:
    -TestOnly           仅运行测试，不构建新镜像
    -CleanupOnly        仅清理测试资源
    -SkipBuild          跳过镜像构建
    -SkipPush           跳过镜像推送
    -SkipTest           跳过容器测试
    -Help               显示此帮助信息

示例:
    .\setup-aliyun.ps1                  # 完整测试流程
    .\setup-aliyun.ps1 -TestOnly        # 仅测试现有镜像
    .\setup-aliyun.ps1 -CleanupOnly     # 清理测试资源

"@
}

# 检查 Docker 环境
function Test-DockerEnvironment {
    Write-Log "检查 Docker 环境..." "Info"
    
    try {
        $dockerVersion = docker --version
        Write-Log "Docker 版本: $dockerVersion" "Info"
    }
    catch {
        Write-Log "Docker 未安装，请先安装 Docker" "Error"
        exit 1
    }
    
    try {
        docker info | Out-Null
        Write-Log "Docker 环境正常" "Success"
    }
    catch {
        Write-Log "Docker 未运行，请启动 Docker" "Error"
        exit 1
    }
}

# 登录阿里云容器镜像服务
function Connect-AliyunRegistry {
    Write-Log "登录阿里云容器镜像服务..." "Info"
    
    try {
        $AliyunPassword | docker login --username $AliyunUsername --password-stdin $AliyunRegistry
        Write-Log "阿里云容器镜像服务登录成功" "Success"
    }
    catch {
        Write-Log "阿里云容器镜像服务登录失败: $_" "Error"
        exit 1
    }
}

# 构建测试镜像
function Build-TestImage {
    Write-Log "构建测试镜像..." "Info"
    
    if (-not (Test-Path "docker\Dockerfile")) {
        Write-Log "Dockerfile 不存在: docker\Dockerfile" "Error"
        exit 1
    }
    
    try {
        docker build -t "test-frontend:local" -f docker\Dockerfile .
        Write-Log "测试镜像构建成功" "Success"
    }
    catch {
        Write-Log "测试镜像构建失败: $_" "Error"
        exit 1
    }
}

# 推送测试镜像
function Push-TestImage {
    Write-Log "推送测试镜像到阿里云..." "Info"
    
    # 生成时间戳标签
    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $aliyunImage = "$AliyunRegistry/$AliyunNamespace/$AliyunRepo`:test-$timestamp"
    
    try {
        # 标记镜像
        docker tag "test-frontend:local" $aliyunImage
        
        # 推送镜像
        docker push $aliyunImage
        
        Write-Log "测试镜像推送成功: $aliyunImage" "Success"
        
        # 保存镜像信息
        $aliyunImage | Out-File -FilePath "$env:TEMP\aliyun_test_image.txt" -Encoding UTF8
        
        return $aliyunImage
    }
    catch {
        Write-Log "测试镜像推送失败: $_" "Error"
        exit 1
    }
}

# 拉取并测试镜像
function Test-PullImage {
    Write-Log "测试镜像拉取..." "Info"
    
    $imageFile = "$env:TEMP\aliyun_test_image.txt"
    if (-not (Test-Path $imageFile)) {
        Write-Log "未找到推送的测试镜像信息，跳过拉取测试" "Warning"
        return
    }
    
    $pushedImage = Get-Content $imageFile -Raw
    $pushedImage = $pushedImage.Trim()
    
    try {
        # 删除本地镜像
        docker rmi $pushedImage 2>$null
        
        # 重新拉取镜像
        docker pull $pushedImage
        
        Write-Log "镜像拉取测试成功" "Success"
    }
    catch {
        Write-Log "镜像拉取测试失败: $_" "Error"
        exit 1
    }
}

# 运行容器测试
function Test-Container {
    Write-Log "测试容器运行..." "Info"
    
    $imageFile = "$env:TEMP\aliyun_test_image.txt"
    if (-not (Test-Path $imageFile)) {
        Write-Log "未找到测试镜像信息，跳过容器测试" "Warning"
        return
    }
    
    $pushedImage = Get-Content $imageFile -Raw
    $pushedImage = $pushedImage.Trim()
    $containerName = "aliyun-test-frontend"
    
    try {
        # 运行测试容器
        docker run -d --name $containerName -p 3000:3000 $pushedImage
        
        Write-Log "测试容器启动成功" "Success"
        
        # 等待容器启动
        Start-Sleep -Seconds 10
        
        # 检查容器状态
        $containerStatus = docker ps --filter "name=$containerName" --format "table {{.Names}}"
        if ($containerStatus -match $containerName) {
            Write-Log "容器运行正常" "Success"
            
            # 测试 HTTP 访问
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing -TimeoutSec 10
                Write-Log "HTTP 访问测试成功" "Success"
            }
            catch {
                Write-Log "HTTP 访问测试失败，但容器正在运行" "Warning"
            }
        }
        else {
            Write-Log "容器启动失败" "Error"
        }
        
        # 清理测试容器
        docker stop $containerName | Out-Null
        docker rm $containerName | Out-Null
        Write-Log "测试容器已清理" "Info"
    }
    catch {
        Write-Log "测试容器启动失败: $_" "Error"
    }
}

# 清理测试资源
function Clear-TestResources {
    Write-Log "清理测试资源..." "Info"
    
    # 删除本地测试镜像
    try {
        docker rmi "test-frontend:local" 2>$null
    }
    catch {
        # 忽略错误
    }
    
    # 删除推送的测试镜像（可选）
    $imageFile = "$env:TEMP\aliyun_test_image.txt"
    if (Test-Path $imageFile) {
        $pushedImage = Get-Content $imageFile -Raw
        $pushedImage = $pushedImage.Trim()
        
        $confirmation = Read-Host "是否删除推送的测试镜像 $pushedImage? (y/N)"
        if ($confirmation -eq "y" -or $confirmation -eq "Y") {
            try {
                docker rmi $pushedImage 2>$null
                Write-Log "已删除推送的测试镜像" "Info"
            }
            catch {
                # 忽略错误
            }
        }
        
        Remove-Item $imageFile -Force -ErrorAction SilentlyContinue
    }
    
    Write-Log "清理完成" "Success"
}

# 显示 GitHub Secrets 配置提示
function Show-GitHubSecrets {
    Write-Log "GitHub Secrets 配置提示：" "Info"
    
    @"

请在 GitHub 仓库中添加以下 Secrets：

1. 进入 GitHub 仓库页面
2. 点击 Settings > Secrets and variables > Actions
3. 添加以下 Repository secrets：

   ALIYUN_REGISTRY_USERNAME=$AliyunUsername
   ALIYUN_REGISTRY_PASSWORD=$AliyunPassword

配置完成后，推送代码到 main 或 develop 分支，
CI/CD 流程将自动构建并推送镜像到阿里云。

"@
}

# 主函数
function Main {
    # 显示帮助
    if ($Help) {
        Show-Help
        exit 0
    }
    
    Show-Welcome
    Show-Config
    
    # 如果只是清理
    if ($CleanupOnly) {
        Clear-TestResources
        exit 0
    }
    
    # 检查 Docker 环境
    Test-DockerEnvironment
    
    # 登录阿里云
    Connect-AliyunRegistry
    
    # 如果不是仅测试模式
    if (-not $TestOnly) {
        # 构建镜像
        if (-not $SkipBuild) {
            Build-TestImage
        }
        
        # 推送镜像
        if (-not $SkipPush) {
            Push-TestImage
        }
    }
    
    # 测试镜像拉取
    if (-not $SkipTest) {
        Test-PullImage
        Test-Container
    }
    
    # 显示 GitHub Secrets 配置提示
    Show-GitHubSecrets
    
    Write-Log "阿里云容器镜像服务配置和测试完成！" "Success"
    
    # 询问是否清理
    $cleanup = Read-Host "是否清理测试资源? (y/N)"
    if ($cleanup -eq "y" -or $cleanup -eq "Y") {
        Clear-TestResources
    }
}

# 运行主函数
Main
