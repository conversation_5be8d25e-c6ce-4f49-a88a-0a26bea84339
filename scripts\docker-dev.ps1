# Docker 开发环境启动脚本
# 使用 .env.development 配置启动 Docker Compose

Write-Host "🚀 启动 Docker 开发环境..." -ForegroundColor Green

# 检查 Docker 是否运行
Write-Host "📋 检查 Docker 状态..." -ForegroundColor Yellow
try {
    docker info | Out-Null
    Write-Host "✅ Docker 运行正常" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker 未运行，请先启动 Docker Desktop" -ForegroundColor Red
    exit 1
}

# 设置工作目录
$projectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $projectRoot

# 检查环境变量文件
Write-Host "📋 检查环境配置..." -ForegroundColor Yellow
if (-not (Test-Path ".env.development")) {
    Write-Host "❌ 未找到 .env.development 文件" -ForegroundColor Red
    exit 1
}

Write-Host "✅ 找到 .env.development 文件" -ForegroundColor Green

# 停止可能存在的容器
Write-Host "🛑 停止现有容器..." -ForegroundColor Yellow
docker-compose -f docker/docker-compose.yml down

# 使用 .env.development 启动服务
Write-Host "🔨 使用开发环境配置启动服务..." -ForegroundColor Yellow
$env:COMPOSE_FILE = "docker/docker-compose.yml"

# 加载 .env.development 并启动
docker-compose --env-file .env.development -f docker/docker-compose.yml up -d --build

# 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# 检查服务状态
Write-Host "📋 检查服务状态..." -ForegroundColor Yellow
docker-compose -f docker/docker-compose.yml ps

# 显示服务地址
Write-Host ""
Write-Host "🎉 Docker 开发环境启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📍 服务地址:" -ForegroundColor Cyan
Write-Host "   - 前端应用: http://localhost:3000" -ForegroundColor White
Write-Host "   - 后端API: http://localhost:5000" -ForegroundColor White
Write-Host "   - MySQL数据库: localhost:13306" -ForegroundColor White
Write-Host ""
Write-Host "📝 常用命令:" -ForegroundColor Cyan
Write-Host "   - 查看日志: docker-compose -f docker/docker-compose.yml logs -f" -ForegroundColor White
Write-Host "   - 停止服务: docker-compose -f docker/docker-compose.yml down" -ForegroundColor White
Write-Host "   - 重启服务: docker-compose -f docker/docker-compose.yml restart" -ForegroundColor White
Write-Host ""
Write-Host "🔧 环境配置:" -ForegroundColor Cyan
Write-Host "   - 使用配置文件: .env.development" -ForegroundColor White
Write-Host "   - 环境模式: development" -ForegroundColor White
Write-Host "   - 热重载: 已启用" -ForegroundColor White
Write-Host ""
Write-Host "💡 提示:" -ForegroundColor Yellow
Write-Host "   - 代码更改会自动反映到容器中" -ForegroundColor White
Write-Host "   - 数据库数据会持久化保存" -ForegroundColor White
Write-Host "   - 可以使用任何MySQL客户端连接到 localhost:13306" -ForegroundColor White
