// 测试任务管理API的简单脚本
// 运行方式: node test/test-tasks-api.js

const BASE_URL = 'http://localhost:3000';

// 测试获取任务列表
async function testGetTasks() {
  try {
    console.log('🧪 测试获取任务列表...');
    const response = await fetch(`${BASE_URL}/api/database/tasks`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const tasks = await response.json();
    console.log('✅ 获取任务列表成功');
    console.log(`📊 共找到 ${tasks.length} 个任务`);
    
    if (tasks.length > 0) {
      console.log('📝 第一个任务示例:');
      console.log({
        task_id: tasks[0].task_id,
        ticker: tasks[0].ticker,
        title: tasks[0].title,
        status: tasks[0].status,
        created_at: tasks[0].created_at
      });
    }
    
    return tasks;
  } catch (error) {
    console.error('❌ 获取任务列表失败:', error.message);
    return null;
  }
}

// 测试获取消息
async function testGetMessages(taskId) {
  try {
    console.log(`\n🧪 测试获取任务 ${taskId} 的消息...`);
    const response = await fetch(`${BASE_URL}/api/database/messages?task_id=${taskId}`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log('✅ 获取消息成功');
    console.log(`📊 共找到 ${data.messages.length} 条消息`);
    
    if (data.messages.length > 0) {
      console.log('📝 第一条消息示例:');
      console.log({
        message_type: data.messages[0].message_type,
        content: data.messages[0].content.substring(0, 100) + '...',
        created_at: data.messages[0].created_at
      });
    }
    
    return data.messages;
  } catch (error) {
    console.error('❌ 获取消息失败:', error.message);
    return null;
  }
}

// 测试启动任务分析
async function testStartAnalysis(ticker) {
  try {
    console.log(`\n🧪 测试启动 ${ticker} 的分析任务...`);
    const response = await fetch(`${BASE_URL}/api/langgraph/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ticker: ticker,
        config: {
          analysisType: 'comprehensive',
          includeRisk: true,
          includeSentiment: true,
        },
      }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    
    const result = await response.json();
    console.log('✅ 启动分析成功');
    console.log('📊 分析结果:', result);
    
    return result;
  } catch (error) {
    console.error('❌ 启动分析失败:', error.message);
    return null;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试任务管理API...\n');
  
  // 测试1: 获取任务列表
  const tasks = await testGetTasks();
  
  if (tasks && tasks.length > 0) {
    // 测试2: 获取第一个任务的消息
    await testGetMessages(tasks[0].task_id);
    
    // 测试3: 启动分析（使用第一个任务的股票代码）
    await testStartAnalysis(tasks[0].ticker);
  } else {
    console.log('\n⚠️  没有找到任务，跳过消息和分析测试');
    
    // 使用默认股票代码测试分析
    await testStartAnalysis('AAPL');
  }
  
  console.log('\n🎉 测试完成！');
}

// 运行测试
runTests().catch(console.error);
