# 开发环境配置
NODE_ENV=development

# API配置 - 本地开发环境
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000
NEXT_PUBLIC_API_BACKEND_BASE_URL=http://localhost:5000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
BACK_END_URL=http://localhost:5000
FRONTEND_URL=http://localhost:3000

# 第三方API密钥 - 开发环境
NEXT_PUBLIC_OPENAI_API_KEY=sk-nBa5UdWvrDVYgsMYKhctz0BRK9l5JLuRzlNR7KJSyyWvFNqS
NEXT_PUBLIC_FINNHUB_API_KEY=your-finnhub-api-key

# 数据库配置 - 本地数据库
DB_HOST=localhost
DB_PORT=13306
MYSQL_ROOT_PASSWORD=trading123
MYSQL_DATABASE=trading_analysis
MYSQL_USER=trading_user
MYSQL_PASSWORD=trading123
# 数据库连接别名
DB_NAME=trading_analysis
DB_USER=root
DB_PASSWORD=trading123

# 开发环境特定配置
NEXT_TELEMETRY_DISABLED=1